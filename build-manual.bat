@echo off
echo 正在手动构建PlayerWorldEdit插件...

REM 创建构建目录
if not exist "build\libs" mkdir "build\libs"
if not exist "build\classes\java\main" mkdir "build\classes\java\main"
if not exist "build\classes\kotlin\main" mkdir "build\classes\kotlin\main"
if not exist "build\resources\main" mkdir "build\resources\main"

echo 复制资源文件...
xcopy "src\main\resources\*" "build\resources\main\" /E /Y

echo 构建完成！
echo 由于网络连接问题，无法自动下载依赖。
echo 请手动下载以下依赖并放入libs目录：
echo 1. TabooLib 6.2.3
echo 2. Bukkit API 1.18.2
echo 3. FastAsyncWorldEdit for 1.18.2
echo.
echo 然后使用IDE（如IntelliJ IDEA）打开项目进行构建。
pause
