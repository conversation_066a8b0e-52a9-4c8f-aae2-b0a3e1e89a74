# PlayerWorldEdit 1.18.2版本构建说明

## 当前状态
由于网络连接问题，无法自动下载Gradle和相关依赖。已为您准备了1.18.2版本的配置文件。

## 已完成的配置更改

### 1. 版本更新
- `gradle.properties`: 版本号更新为 `1.18.2-1.0`
- `build.gradle.kts`: 核心依赖更新为1.18.2版本 (`v11802`)

### 2. 创建的文件
- `plugin.yml`: 标准Bukkit插件配置文件，适配1.18.2
- `build-manual.bat`: 手动构建脚本

## 手动构建方法

### 方法一：使用IDE构建（推荐）

1. **安装IntelliJ IDEA**（推荐）或Eclipse
2. **导入项目**：
   - 打开IDE
   - 选择"Import Project"或"Open"
   - 选择项目根目录
   - 选择"Gradle"项目类型

3. **配置项目**：
   - 确保使用Java 8
   - 等待IDE自动下载依赖（需要网络连接）

4. **构建插件**：
   - 在IDE中运行Gradle任务：`build`
   - 或使用IDE的Build菜单

### 方法二：离线构建

如果网络连接持续有问题，可以：

1. **下载必需的依赖**：
   - TabooLib 6.2.3
   - Bukkit API 1.18.2
   - FastAsyncWorldEdit 1.18.2版本
   - Kotlin标准库

2. **手动编译**：
   ```bash
   # 编译Java文件
   javac -cp "libs/*" -d build/classes src/main/java/**/*.java
   
   # 编译Kotlin文件（需要kotlinc）
   kotlinc -cp "libs/*" -d build/classes src/main/kotlin/**/*.kt
   
   # 创建JAR文件
   jar cf PlayerWorldEdit-1.18.2-1.0.jar -C build/classes . -C src/main/resources .
   ```

## 依赖要求

### 服务器端依赖
- **Minecraft版本**: 1.18.2
- **服务器类型**: Bukkit/Spigot/Paper
- **必需插件**: FastAsyncWorldEdit (1.18.2兼容版本)

### 开发依赖
- **Java**: 8或更高版本
- **Kotlin**: 1.8.22
- **TabooLib**: 6.2.3
- **Bukkit API**: 1.18.2

## 功能特性

这个插件为1.18.2版本提供以下功能：

1. **生存模式WorldEdit**：
   - 在生存模式中使用创世神功能
   - 特殊的创世神镐工具
   - 区域选择和编辑

2. **材料背包系统**：
   - 专用的材料存储界面
   - 快速取出功能

3. **命令系统**：
   - `/pwe` - 主命令
   - `/pwe tool` - 获取创世神镐
   - `/pwe set <材料>` - 设置区域方块
   - `/pwe replace <原材料> <新材料>` - 替换方块

## 安装说明

1. 确保服务器运行Minecraft 1.18.2
2. 安装FastAsyncWorldEdit插件
3. 将构建好的PlayerWorldEdit jar文件放入plugins目录
4. 重启服务器

## 权限配置

- `PlayerWorldEdit.Use` - 基本使用权限（默认所有玩家）
- `PlayerWorldEdit.Admin` - 管理员权限（默认OP）

## 注意事项

- 此插件依赖FastAsyncWorldEdit，请确保先安装该插件
- 建议在测试服务器上先测试功能
- 1.18.2版本的兼容性已经过配置调整

## 故障排除

如果遇到构建问题：
1. 检查Java版本是否为8或更高
2. 确保网络连接正常（用于下载依赖）
3. 尝试使用VPN或代理
4. 考虑使用离线构建方法
