PlayerWorldEdit 1.18.2版本 - 快速构建指南
===========================================

已完成的配置更改：
✅ 版本号更新为 1.18.2-1.0
✅ 核心依赖更新为1.18.2版本
✅ 创建了标准plugin.yml文件
✅ 优化了Gradle配置

构建方法（按优先级排序）：

方法1：使用IntelliJ IDEA（最推荐）
1. 下载并安装IntelliJ IDEA Community版（免费）
2. 打开项目：File -> Open -> 选择项目文件夹
3. 等待IDE自动导入和下载依赖
4. 点击右侧Gradle面板 -> Tasks -> build -> build
5. 构建完成后在 build/libs/ 目录找到jar文件

方法2：使用Eclipse
1. 安装Eclipse IDE for Java Developers
2. 安装Gradle插件（Buildship）
3. Import -> Existing Gradle Project
4. 右键项目 -> Gradle -> Refresh Gradle Project
5. 右键项目 -> Run As -> Gradle Build -> 输入"build"

方法3：命令行构建（需要网络）
1. 打开命令提示符
2. 进入项目目录
3. 运行：gradlew.bat build
4. 等待构建完成

方法4：在线构建服务
1. 将项目上传到GitHub
2. 使用GitHub Actions自动构建
3. 或使用其他CI/CD服务

构建成功后：
- jar文件位置：build/libs/PlayerWorldEdit-1.18.2-1.0.jar
- 将jar文件放入Minecraft 1.18.2服务器的plugins目录
- 确保已安装FastAsyncWorldEdit插件
- 重启服务器即可使用

如果遇到网络问题：
- 尝试使用VPN
- 使用手机热点
- 在网络环境好的地方构建

联系方式：
如需帮助，请提供具体的错误信息。
